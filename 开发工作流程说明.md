# E7装备OCR助手 开发工作流程说明

## 项目概述

E7装备OCR助手是一个第七史诗游戏装备识别和评估系统，使用Rust+Tauri+Vue3架构以获得最佳性能和用户体验。本项目采用桌面应用架构，通过ADB连接Android设备进行截图和OCR识别。

## UI界面外观

### 主界面布局
应用采用左侧导航栏 + 右侧内容区域的经典布局：

**导航栏：**
- 位于界面左侧
- 包含两个导航项：
  - 装备助手 (相机图标)
  - 参数配置 (齿轮图标)
- 支持图标+文字标签显示
- 当前选中项高亮显示
- 底部显示ADB连接状态（手机图标+状态文字）

**内容区域：**
- 占据界面右侧主要空间
- 根据导航选择动态切换页面内容
- 使用简洁的分区布局，使用分隔线作为区分

### 装备助手页面
**整体布局：** 双列布局，左右各占50%空间

**左侧列：**
1. **控制区域** - 左列第一个
   - 页面标题："装备识别助手" (大号粗体字)
   - 识别按钮：蓝色主要按钮，带相机图标

2. **分数显示区域** - 左列第二个
   - 标题："装备分数"
   - 大号粗体数字显示分数 (如 "45/55")
   - 分数达标时显示绿色，不达标显示红色
   - 强化建议文本

3. **装备信息区域** - 左列第三个
   - 标题："装备信息"
   - 装备基本信息以键值对形式显示：
     - 稀有度、类型、强化等级、套装
   - 主属性和副属性列表
   - 每行左侧显示属性名，右侧显示数值

**右侧列：**
1. **图片预览区域** - 右上方
   - 标题："截图预览"
   - 显示处理后的装备截图
   - 图片自适应缩放

2. **运行日志区域** - 右下方
   - 标题："运行日志"
   - 可选择的文本区域
   - 显示日志信息
   - 支持滚动查看完整内容

### 设置页面
**整体布局：** 从上到下的垂直布局

**页面头部：**
- 标题："系统设置" (大号粗体字)
- 保存按钮：带保存图标的主要按钮
- 状态指示器：显示当前初始化状态

**设置区域（垂直排列）：**
1. **ADB设置区域**
   - 区域标题："ADB连接设置"
   - ADB端口输入框 (默认16384)
   - 连接超时设置
   - 自动连接开关
   - 连接按钮和状态显示

2. **热键设置区域**
   - 区域标题："热键设置"
   - 启用热键开关
   - 热键输入框 (如F1)

3. **OCR设置区域**
   - 区域标题："OCR识别设置"
   - 页面识别设置：边框阈值、识别阈值
   - 装备识别设置：检测阈值、边框阈值、识别阈值

4. **装备分数设置区域**
   - 区域标题："装备分数标准"
   - 左三装备分数标准表格
   - 右三装备分数标准表格
   - 速度属性分数标准表格
   - 重铸界面分数标准表格
   - 每个表格包含装备等级和对应分数标准

## 1. 项目架构设计

### 1.1 技术栈架构
```
前端层 (Vue3 + TypeScript)
├── 用户界面组件
├── 状态管理 (Vue3响应式数据)
├── 路由管理 (Vue Router)
└── Tauri API调用

中间层 (Tauri)
├── 前后端通信桥梁
├── 系统API访问
└── 安全沙箱

后端层 (Rust)
├── ADB连接管理
├── 图像处理
├── OCR识别
├── 装备评分算法
└── 配置管理
```

### 1.2 目录结构
```
E7gearOcr/
├── src/                    # Vue前端源码
│   ├── components/         # 组件目录
│   ├── views/             # 页面视图
│   ├── router/            # 路由配置
│   ├── types/             # TypeScript类型定义
│   └── utils/             # 工具函数
├── src-tauri/             # Rust后端源码
│   ├── src/               # Rust源码
│   ├── PaddleOCR-json/    # OCR引擎
│   ├── pages_sign/        # 页面识别标志图片
│   └── config.json        # 配置文件
└── public/                # 静态资源
```

### 1.3 数据流架构
```
用户操作 → Vue组件 → Tauri命令 → Rust后端 → ADB设备 → 图像处理 → OCR识别 → 装备评分 → 结果返回 → 前端显示
```

## 2. 前端开发流程

### 2.1 项目初始化
1. 创建Tauri+Vue3项目
2. 配置TypeScript支持
3. 安装必要依赖：Vue Router、Pinia、Element Plus等
4. 配置构建工具和开发环境

### 2.2 左侧导航栏组件开发

#### 2.2.1 NavigationSidebar组件
- **文件位置**: `src/components/NavigationSidebar.vue`
- **功能要求**:
  - 固定左侧布局，宽度约200px
  - 包含两个导航项：装备助手(相机图标)、参数配置(齿轮图标)
  - 支持路由切换和高亮显示
  - 响应式设计
  - **ADB状态显示**：
    - 底部显示ADB连接状态
    - 使用安卓图标和状态文字
    - 连接成功显示绿色，未连接显示红色
    - 监听来自EquipmentAssistant的状态更新事件

#### 2.2.2 实现细节
```typescript
// 导航项数据结构
interface NavItem {
  id: string;
  name: string;
  icon: string;
  route: string;
}

// 导航项配置
const navItems: NavItem[] = [
  { id: 'equipment', name: '装备助手', icon: 'camera', route: '/equipment' },
  { id: 'settings', name: '参数配置', icon: 'settings', route: '/settings' }
];
```

### 2.3 装备助手页面开发

#### 2.3.1 EquipmentAssistant页面
- **文件位置**: `src/views/EquipmentAssistant.vue`
- **布局要求**: 双列布局，左右各占50%

#### 2.3.2 左侧列组件开发

**ControlPanel组件** (`src/components/ControlPanel.vue`)
- 页面标题显示
- 识别按钮实现，调用Tauri命令
- 按钮状态管理（加载中、可用、禁用）

**ScoreDisplay组件** (`src/components/ScoreDisplay.vue`)
- 分数显示逻辑
- 颜色状态管理（绿色达标/红色不达标）
- 强化建议文本显示

**EquipmentInfo组件** (`src/components/EquipmentInfo.vue`)
- 装备基本信息展示
- 主属性和副属性列表
- 键值对格式化显示

#### 2.3.3 右侧列组件开发

**ImagePreview组件** (`src/components/ImagePreview.vue`)
- 截图预览功能
- 图片自适应缩放
- 加载状态处理

**LogDisplay组件** (`src/components/LogDisplay.vue`)
- 运行日志显示
- 滚动查看功能
- 文本选择支持

### 2.4 设置页面开发

#### 2.4.1 Settings页面
- **文件位置**: `src/views/Settings.vue`
- **布局要求**: 垂直布局，从上到下排列

#### 2.4.2 设置区域组件开发

**ADBSettings组件** (`src/components/settings/ADBSettings.vue`)
- ADB端口配置
- 连接超时设置
- 自动连接开关
- 连接状态显示

**HotkeySettings组件** (`src/components/settings/HotkeySettings.vue`)
- 热键启用开关
- 热键输入框
- 热键冲突检测

**OCRSettings组件** (`src/components/settings/OCRSettings.vue`)
- 页面识别参数配置
- 装备识别参数配置
- 阈值调节器

**ScoreSettings组件** (`src/components/settings/ScoreSettings.vue`)
- 装备分数标准表格
- 不同装备类型的分数配置
- 速度属性特殊配置

### 2.5 状态管理设计

#### 2.5.1 响应式状态管理
项目使用Vue3的响应式数据（ref/reactive）进行状态管理，无需Pinia等外部状态管理库：

**装备识别信息管理：**
- 直接从Rust后端获取装备识别结果
- 在EquipmentAssistant.vue组件中使用响应式数据管理
- 包括识别状态、装备信息、评分结果、截图和日志等
- 仅在单个页面中使用，无需跨组件状态同步

**配置信息管理：**
- 每次进入Settings.vue页面时从后端获取配置
- 在组件内部使用响应式数据管理
- 包括ADB设置、热键设置、OCR设置、分数设置等
- 仅在设置页面中使用，无需跨组件状态同步

**ADB连接状态管理：**
- 使用自定义事件进行跨组件状态同步
- EquipmentAssistant.vue检查连接状态并发送'adb-status-update'事件
- NavigationSidebar.vue监听事件并更新底部状态显示
- 这是唯一需要跨组件同步的状态信息
- 无需Pinia等状态管理库，使用浏览器原生事件系统

#### 2.5.2 状态管理优势
- **简化架构**：避免了Pinia等状态管理库的复杂性
- **性能优化**：减少了不必要的状态订阅和更新
- **代码清晰**：状态逻辑集中在使用它的组件内部
- **维护便利**：状态变更影响范围明确，易于调试和维护

## 3. 后端开发流程

### 3.1 Rust项目结构设计

#### 3.1.1 模块划分
```rust
src/
├── main.rs              // 主入口
├── lib.rs               // 库入口
├── commands/            // Tauri命令
│   ├── mod.rs
│   ├── equipment.rs     // 装备识别命令
│   └── settings.rs      // 设置相关命令
├── adb/                 // ADB连接模块
│   ├── mod.rs
│   ├── client.rs        // ADB客户端
│   └── screenshot.rs    // 截图功能
├── ocr/                 // OCR识别模块
│   ├── mod.rs
│   ├── page_detector.rs // 页面类型识别
│   ├── image_processor.rs // 图像处理
│   └── text_extractor.rs // 文本提取
├── equipment/           // 装备处理模块
│   ├── mod.rs
│   ├── parser.rs        // 装备信息解析
│   ├── scorer.rs        // 装备评分
│   └── types.rs         // 装备数据类型
└── config/              // 配置管理模块
    ├── mod.rs
    └── manager.rs       // 配置读写
```

### 3.2 ADB连接和截图功能

#### 3.2.1 ADB客户端实现
```rust
// adb/client.rs
pub struct ADBClient {
    port: u16,
    timeout: Duration,
}

impl ADBClient {
    pub async fn connect(&self) -> Result<ADBConnection, ADBError> {
        // 连接到ADB服务器
        // 发现并连接Android设备
    }
    
    pub async fn take_screenshot(&self) -> Result<PathBuf, ADBError> {
        // 执行screencap命令
        // 下载截图文件到本地
        // 返回本地文件路径
    }
}
```

#### 3.2.2 截图流程实现
1. 连接ADB服务器（默认端口16384）
2. 执行shell命令：`screencap -p /sdcard/screenshot.png`
3. 使用pull操作下载到src-tauri/screenshot目录
4. 返回本地文件路径供后续处理

### 3.3 页面类型识别算法

#### 3.3.1 页面识别核心逻辑
```rust
// ocr/page_detector.rs
pub enum PageType {
    Backpack,      // 背包界面
    Enhancement,   // 强化界面
    Recast,        // 重铸界面
    Other,         // 其他页面
}

impl PageDetector {
    pub fn detect_page_type(&self, image_path: &Path) -> Result<PageType, OCRError> {
        // 1. 确保图像尺寸为1280x720
        // 2. 截取指定区域进行OCR识别
        // 3. 根据识别结果判断页面类型
        // 4. 对于背包和重铸界面进行二次识别
    }
    
    fn extract_title_region(&self, image: &DynamicImage) -> DynamicImage {
        // 截取区域：[y_min=15, y_max=50, x_min=60, x_max=175]
    }
    
    fn compare_with_sign(&self, image: &DynamicImage, sign_type: SignType) -> f32 {
        // 与标志图像进行对比
        // 返回相似度分数
    }
}
```

#### 3.3.2 二次识别实现
- 截取标志区域：[y_min=160, y_max=215, x_min=25, x_max=105]
- 与pages_sign目录中的标志图像对比
- 使用图像相似度算法确定具体页面类型

### 3.4 图像处理和OCR集成

#### 3.4.1 图像预处理
```rust
// ocr/image_processor.rs
impl ImageProcessor {
    pub fn process_backpack_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取装备信息区域：[y_min=165, y_max=585, x_min=875, x_max=1225]
        // 保留四个关键区域，其余设为黑色
        // 返回处理后的图像
    }
    
    pub fn process_enhancement_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取装备信息区域：[y_min=80, y_max=480, x_min=30, x_max=370]
        // 保留四个关键区域，其余设为黑色
    }
    
    pub fn process_recast_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取重铸区域：[y_min=490, y_max=625, x_min=280, x_max=770]
        // 涂黑指定干扰区域
    }
}
```

#### 3.4.2 OCR引擎集成
```rust
// ocr/text_extractor.rs
pub struct OCRExtractor {
    paddle_ocr_path: PathBuf,
}

impl OCRExtractor {
    pub fn extract_text(&self, image_path: &Path) -> Result<Vec<OCRResult>, OCRError> {
        // 调用PaddleOCR-json进行文本识别
        // 参数：--det_limit_type max --det_limit_side_len 96
        // 解析JSON结果返回文本块列表
    }
}
```

### 3.5 装备信息提取逻辑

#### 3.5.1 装备数据结构
```rust
// equipment/types.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Equipment {
    pub rarity: Rarity,           // 稀有度
    pub equipment_type: EquipmentType, // 装备类型
    pub enhancement_level: u8,     // 强化等级
    pub set_type: Option<SetType>, // 套装类型
    pub main_stat: Stat,          // 主属性
    pub sub_stats: Vec<Stat>,     // 副属性
    pub is_recast: bool,          // 是否为重铸界面
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Stat {
    pub stat_type: StatType,
    pub value: f32,
    pub is_percentage: bool,
}
```

#### 3.5.2 信息解析算法
```rust
// equipment/parser.rs
impl EquipmentParser {
    pub fn parse_equipment(&self, ocr_results: Vec<OCRResult>, page_type: PageType) -> Result<Equipment, ParseError> {
        match page_type {
            PageType::Backpack | PageType::Enhancement => {
                self.parse_normal_equipment(ocr_results)
            },
            PageType::Recast => {
                self.parse_recast_equipment(ocr_results)
            },
            _ => Err(ParseError::UnsupportedPageType)
        }
    }
    
    fn extract_rarity_and_type(&self, text_blocks: &[OCRResult]) -> (Rarity, EquipmentType) {
        // 使用正则表达式提取：r"(传说|英雄|稀有|高级)(武器|头盔|铠甲|项链|戒指|鞋子)"
    }
    
    fn extract_enhancement_level(&self, text_blocks: &[OCRResult]) -> u8 {
        // 在前两个文本块中查找强化等级
    }
    
    fn parse_stats(&self, text_blocks: &[OCRResult]) -> (Stat, Vec<Stat>) {
        // 按行排列文本块（y轴坐标差值在10以内视为同一行）
        // 第一行为主属性，其余为副属性
        // 每行包含属性名称和数值
    }
}
```

### 3.6 装备评分系统实现

#### 3.6.1 评分算法
```rust
// equipment/scorer.rs
impl EquipmentScorer {
    pub fn calculate_score(&self, equipment: &Equipment) -> f32 {
        // 分数计算公式：
        // 分数 = 攻击% + 生命% + 防御% + 命中 + 抵抗 + 速度*2 + 暴率*(9/6) + 爆伤*(9/8) + 攻击*3.46/39 + 防御*4.99/31 + 生命*3.09/174
        
        let mut score = 0.0;
        
        for stat in &equipment.sub_stats {
            score += match stat.stat_type {
                StatType::AttackPercent => stat.value,
                StatType::HealthPercent => stat.value,
                StatType::DefensePercent => stat.value,
                StatType::EffectHit => stat.value,
                StatType::EffectResistance => stat.value,
                StatType::Speed => stat.value * 2.0,
                StatType::CriticalRate => stat.value * (9.0 / 6.0),
                StatType::CriticalDamage => stat.value * (9.0 / 8.0),
                StatType::Attack => stat.value * 3.46 / 39.0,
                StatType::Defense => stat.value * 4.99 / 31.0,
                StatType::Health => stat.value * 3.09 / 174.0,
            };
        }
        
        score
    }
    
    pub fn get_recommendation(&self, equipment: &Equipment, score: f32) -> String {
        let standards = self.get_standards_for_equipment(equipment);
        let level_group = self.get_level_group(equipment.enhancement_level);
        let threshold = standards[level_group];
        
        if score >= threshold {
            "建议强化".to_string()
        } else if self.is_speed_equipment(equipment) {
            self.check_speed_potential(equipment)
        } else {
            "不建议强化".to_string()
        }
    }
}
```

#### 3.6.2 强化建议标准
- 武器、头盔、铠甲：[21,28,35,42,49,55]
- 项链、戒指：[19,26,33,40,47,53]
- 鞋子：根据主属性决定使用哪套标准
- 速度装备特殊处理：[3,6,10,12,12,14]

## 4. 配置管理

### 4.1 配置文件结构
```rust
// config/manager.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub adb_settings: ADBConfig,
    pub hotkey_settings: HotkeyConfig,
    pub ocr_settings: OCRConfig,
    pub score_settings: ScoreConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ADBConfig {
    pub port: u16,
    pub timeout_seconds: u64,
    pub auto_connect: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OCRConfig {
    pub page_detection_threshold: f32,
    pub equipment_detection_threshold: f32,
    pub border_threshold: f32,
}
```

### 4.2 配置读写机制
```rust
impl ConfigManager {
    pub fn load_config() -> Result<AppConfig, ConfigError> {
        // 从src-tauri/src/config.json读取配置
        // 如果文件不存在则创建默认配置
    }
    
    pub fn save_config(config: &AppConfig) -> Result<(), ConfigError> {
        // 将配置写入config.json文件
        // 确保文件格式化和错误处理
    }
    
    pub fn get_default_config() -> AppConfig {
        // 返回默认配置值
    }
}
```

## 5. 开发顺序建议

### 5.1 第一阶段：基础架构搭建
1. **项目初始化**(已完成)
   - 创建Tauri+Vue3项目
   - 配置开发环境和构建工具

2. **基础UI框架**
   - 实现左侧导航栏组件
   - 创建基础页面路由
   - 搭建双列和垂直布局框架

3. **配置管理系统**
   - 实现配置文件读写
   - 创建配置数据结构
   - 实现默认配置生成

### 5.2 第二阶段：核心功能实现
4. **ADB连接功能**
   - 实现ADB客户端连接
   - 实现截图功能
   - 添加连接状态管理

5. **页面识别系统**
   - 实现页面类型检测算法
   - 集成图像对比功能
   - 添加识别结果验证

6. **OCR集成**
   - 集成PaddleOCR-json引擎
   - 实现图像预处理
   - 实现文本提取和解析

### 5.3 第三阶段：装备处理系统
7. **装备信息解析**
   - 实现装备数据结构
   - 实现信息提取算法
   - 添加解析结果验证

8. **装备评分系统**
   - 实现评分算法
   - 实现强化建议逻辑
   - 添加特殊情况处理

### 5.4 第四阶段：UI完善和集成
9. **前端组件完善**
   - 完成所有UI组件实现
   - 添加状态管理和数据绑定
   - 实现用户交互逻辑

10. **系统集成测试**
    - 端到端功能测试
    - 性能优化
    - 错误处理完善

### 5.5 第五阶段：优化和发布
11. **功能优化**
    - 识别准确率优化
    - 用户体验改进
    - 性能调优

12. **发布准备**
    - 打包配置优化
    - 文档编写
    - 版本发布

## 6. 技术实现细节

### 6.1 关键算法参数

#### 6.1.1 OCR识别参数
- PaddleOCR参数：`--det_limit_type max --det_limit_side_len 96`
- 页面识别区域：[y_min=15, y_max=50, x_min=60, x_max=175]
- 标志识别区域：[y_min=160, y_max=215, x_min=25, x_max=105]

#### 6.1.2 图像处理区域
**背包界面**：
- 截取区域：[y_min=165, y_max=585, x_min=875, x_max=1225]
- 保留区域：4个关键信息块

**强化界面**：
- 截取区域：[y_min=80, y_max=480, x_min=30, x_max=370]
- 保留区域：4个关键信息块

**重铸界面**：
- 截取区域：[y_min=490, y_max=625, x_min=280, x_max=770]
- 涂黑区域：[y_min=0, y_max=135, x_min=370, x_max=430]

### 6.2 数据流程图

```
用户点击识别按钮
    ↓
调用recognize_equipment命令
    ↓
ADB连接设备并截图
    ↓
页面类型识别
    ↓
图像预处理（根据页面类型）
    ↓
OCR文本识别
    ↓
装备信息解析
    ↓
装备评分计算
    ↓
返回结果到前端显示
```

### 6.3 错误处理策略

#### 6.3.1 ADB连接错误
- 连接超时处理
- 设备未找到处理
- 权限不足处理

#### 6.3.2 OCR识别错误
- 图像格式错误处理
- 识别结果为空处理
- 文本解析失败处理

#### 6.3.3 装备解析错误
- 未知装备类型处理
- 属性解析失败处理
- 数据格式错误处理

## 核心功能流程

### 1. 装备识别完整流程

#### 1.1 触发识别
- 用户点击"装备识别"按钮
- 前端调用Tauri命令: `recognize_equipment`
- 后端开始截图和识别流程

#### 1.2 设备连接与截图
1. 使用adb_client连接到指定端口的ADB服务器
2. 发现并连接目标Android设备
3. 执行shell命令进行截图：screencap -p /sdcard/screenshot.png
4. 使用pull操作下载截图文件到src-tauri/screenshot目录
5. 返回截图文件路径供后续处理

#### 1.3 页面类型识别
页面分为背包界面、强化界面、重铸装备页面和其他页面。

**页面识别核心逻辑：**
1. 确保图形尺寸是1280*720
2. 获取截图中[y_min = 15；y_max = y_min + 35； x_min = 60；x_max = x_min + 115]的区域
3. 对该区域进行OCR识别，使用paddleocr-json(位于src-tauri\PaddleOCR-json下同)参数：--det_limit_type max --det_limit_side_len 96
4. 根据识别结果判断页面类型：
   - 如果是"强化装备"则为强化装备页面
   - 如果是"背包"或"钢铁工坊"则需要后续识别
   - 如果是其他文字则为其他页面

**背包和重铸界面的二次识别：**
1. 截取截图中的[y_min = 160；y_max = y_min + 55；x_min = 25；x_max = x_min + 80]的区域
2. 与标志图像进行对比，标志在src-tauri/pages_sign目录：
   - Backpack_sign.png为背包界面标志
   - Recast_sign.png为重铸装备界面标志
3. 使用图像比对函数确定具体页面类型
4. 当识别到其他页面时直接停止识别

#### 1.4 页面图像处理
不同页面需要截取不同的装备信息区域：

**背包界面处理：**
1. 截取区域：[y_min = 165；y_max = y_min + 420； x_min = 875；x_max = x_min + 350]
2. 对截取的图片进行处理，保留以下四块区域，其余区域设置为黑色：
   - [y_min = 0；y_max = y_min + 35； x_min = 45；x_max = x_min + 45]
   - [y_min = 15；y_max = y_min + 32； x_min = 90；x_max = x_min + 80]
   - [y_min = 155；y_max = y_min + 180； x_min = 0；x_max = x_min + 350]
   - [y_min = 385；y_max = y_min + 35； x_min = 40；x_max = x_min + 200]
3. 使用paddleocr-json对处理后的图像进行OCR识别

**强化界面处理：**
1. 截取区域：[y_min = 80；y_max = y_min + 400； x_min = 30；x_max = x_min + 340]
2. 对截取的图片进行处理，保留以下四块区域，其余区域设置为黑色：
   - [y_min = 0；y_max = y_min + 35； x_min = 60；x_max = x_min + 45]
   - [y_min = 17；y_max = y_min + 23； x_min = 105；x_max = x_min + 75]
   - [y_min = 135；y_max = y_min + 215； x_min = 0；x_max = x_min + 340]
   - [y_min = 365；y_max = y_min + 35； x_min = 43；x_max = x_min + 200]
3. 使用paddleocr-json对处理后的图像进行OCR识别

**重铸界面处理：**
1. 截取区域：[y_min = 490；y_max = y_min + 135； x_min = 280；x_max = x_min + 490]
2. 对截取的图片进行处理，将[y_min = 0；y_max = y_min + 135； x_min = 370；x_max = x_min + 60]区域涂黑
3. 使用paddleocr-json对处理后的图像进行OCR识别

#### 1.5 装备信息提取

**装备基础信息：**
- 装备稀有度：传说、英雄、稀有、高级
- 装备部位：武器、头盔、铠甲、项链、戒指、鞋子
- 装备强化等级：+0到+15，装备+0时不显示数字
- 装备属性：["速度", "生命值", "防御力", "攻击力", "暴击率", "暴击伤害", "效果抗性", "效果命中"]
- 套装类型：["攻击套装", "生命值套装", "防御套装", "速度套装", "暴击套装", "破灭套装", "命中套装", "抵抗套装", "反击套装", "吸血套装", "免疫套装", "夹攻套装", "愤怒套装", "穿透套装", "憎恨套装", "伤口套装", "守护套装", "激流套装"]

**属性规则：**
- "生命值", "防御力", "攻击力"分为固定值和百分比
- 主属性和副属性不会重复
- 武器主属性一定为固定值攻击力
- 头盔主属性一定为固定值生命值
- 铠甲主属性一定为固定值防御力
- 项链戒指鞋子主属性不固定
- 装备稀有度影响副属性数量：传说级装备初始4条副属性，英雄级装备初始3条副属性在+12时获取第四条

**背包和强化界面信息提取流程：**
1. 通过正则表达式提取稀有度和部位：r"(传说|英雄|稀有|高级)(武器|头盔|铠甲|项链|戒指|鞋子)"
2. 强化等级只会在前两个文本块内出现
3. 将已使用的文本块从OCR结果中剔除
4. 将剩余文本块按行排列，y轴坐标在10以内的视作一行
5. 通过装备属性名称定位含有主属性和副属性的行
6. 从上到下第一行为主属性，其余为副属性
7. 每一行中至少有两个文本块，一个包含属性名称，另一个包含数值
8. 一个属性对应一个数值

**重铸界面信息提取：**
1. 仅需要获取主属性和副属性
2. 一个属性名称对应两个数值：从左到右第一个为重铸前数值，第二个为重铸后数值
3. 强化等级标为15
4. 标记为重铸界面

#### 1.6 装备评分系统

**分数计算公式：**
分数 = 攻击% + 生命% + 防御% + 命中 + 抵抗 + 速度 * 2 + 暴率 * (9/6) + 爆伤 * (9/8) + 攻击 * 3.46 / 39 + 防御 * 4.99 / 31 + 生命 * 3.09 / 174

**强化建议标准：**
- 武器、头盔、铠甲默认标准：21,28,35,42,49,55 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15
- 项链、戒指默认标准：19,26,33,40,47,53 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15
- 鞋子：主属性是速度时使用项链戒指标准，其他时使用武器头盔铠甲标准

**速度装备特殊处理：**
- 武器、头盔、铠甲、项链、戒指含有速度副属性且不满足常规标准时可作为赌速度装备
- 速度属性值标准：3,6,10,12,12,14 对应强化等级+0-2，+3-5，+6-8，+9-11，+12-14，+15

**重铸界面评分：**
- 计算重铸前后分数差
- 武器、头盔、铠甲标准为67
- 项链、戒指、鞋子标准为65

### 2. 识别配置

#### 2.1 识别配置文件
1. 每次启动之前从配置文件读取配置。
2. 每次关闭程序时，将配置写入json文件。
3. 配置文件应该位于src-tauri\src\config.json。
4. 配置文件包含设置页面中的所有参数

这份开发工作流程说明涵盖了requirements.md中的所有功能点，提供了详细的技术实现指导和开发顺序建议。开发团队可以按照这个流程逐步实现E7装备OCR助手的完整功能。
