{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 8040865611140527743, "deps": [[654232091421095663, "tauri_utils", false, 1369637879711117647], [4824857623768494398, "cargo_toml", false, 11837033948236007126], [4899080583175475170, "semver", false, 10679759140039870386], [6913375703034175521, "schemars", false, 4156230284441313259], [7170110829644101142, "json_patch", false, 14645394742999260025], [8569119365930580996, "serde_json", false, 11409393213324749493], [9689903380558560274, "serde", false, 15111251786254778942], [12714016054753183456, "tauri_winres", false, 10099565976736845574], [13077543566650298139, "heck", false, 4270090852814317102], [13625485746686963219, "anyhow", false, 11743789584046627553], [15609422047640926750, "toml", false, 3107278618008413205], [15622660310229662834, "walkdir", false, 13627003180559615160], [16928111194414003569, "dirs", false, 6272815632263366733], [17155886227862585100, "glob", false, 14140733676001939860]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-70f1cc8509da6f4b\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}