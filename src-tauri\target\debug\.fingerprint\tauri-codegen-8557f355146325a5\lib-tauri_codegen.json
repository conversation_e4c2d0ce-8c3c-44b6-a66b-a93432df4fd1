{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 7802507678539087609, "deps": [[654232091421095663, "tauri_utils", false, 1369637879711117647], [3060637413840920116, "proc_macro2", false, 8449372680968289944], [3150220818285335163, "url", false, 3921092854587968189], [4899080583175475170, "semver", false, 10679759140039870386], [4974441333307933176, "syn", false, 15715211843743120544], [7170110829644101142, "json_patch", false, 14645394742999260025], [7392050791754369441, "ico", false, 12858652719482957169], [8319709847752024821, "uuid", false, 13541816783514181873], [8569119365930580996, "serde_json", false, 11409393213324749493], [9556762810601084293, "brotli", false, 3056490939896555522], [9689903380558560274, "serde", false, 15111251786254778942], [9857275760291862238, "sha2", false, 16119881872194073357], [10806645703491011684, "thiserror", false, 7309883229814674106], [12687914511023397207, "png", false, 12019600900970735395], [13077212702700853852, "base64", false, 6746830825205552479], [15622660310229662834, "walkdir", false, 13627003180559615160], [17990358020177143287, "quote", false, 18198098267329608094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-8557f355146325a5\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}