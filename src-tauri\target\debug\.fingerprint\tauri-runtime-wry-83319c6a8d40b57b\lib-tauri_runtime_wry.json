{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 4933143238336490359, "deps": [[376837177317575824, "softbuffer", false, 5000444160280601406], [654232091421095663, "tauri_utils", false, 6312412895848897844], [2013030631243296465, "webview2_com", false, 3033330217559763731], [3150220818285335163, "url", false, 12542923243119232473], [3722963349756955755, "once_cell", false, 8172514313461330740], [4143744114649553716, "raw_window_handle", false, 13938569619552517521], [5986029879202738730, "log", false, 2334386541417879784], [8826339825490770380, "tao", false, 8050325609141343412], [9010263965687315507, "http", false, 16749436415575297115], [9141053277961803901, "wry", false, 1800309252151985860], [12304025191202589669, "build_script_build", false, 4244514533921553955], [12943761728066819757, "tauri_runtime", false, 9315366099735250839], [14585479307175734061, "windows", false, 9741366053200353985]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-83319c6a8d40b57b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}