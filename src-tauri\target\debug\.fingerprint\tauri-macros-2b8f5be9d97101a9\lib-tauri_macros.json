{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 2703567358352027674, "deps": [[654232091421095663, "tauri_utils", false, 1369637879711117647], [2704937418414716471, "tauri_codegen", false, 6066049674899572922], [3060637413840920116, "proc_macro2", false, 8449372680968289944], [4974441333307933176, "syn", false, 15715211843743120544], [13077543566650298139, "heck", false, 4270090852814317102], [17990358020177143287, "quote", false, 18198098267329608094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-2b8f5be9d97101a9\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}