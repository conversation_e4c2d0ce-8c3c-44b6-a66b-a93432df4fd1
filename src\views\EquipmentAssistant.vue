<template>
  <div class="equipment-assistant">
    <div class="left-column">
      <!-- 控制区域 -->
      <div class="control-panel">
        <h1 class="page-title">装备识别助手</h1>
        <el-button 
          type="primary" 
          size="large"
          :loading="isRecognizing"
          @click="startRecognition"
          class="recognition-btn"
        >
          <el-icon><Camera /></el-icon>
          {{ isRecognizing ? '识别中...' : '开始识别' }}
        </el-button>
      </div>

      <!-- 分数显示区域 -->
      <div class="score-display">
        <h3 class="section-title">装备分数</h3>
        <div class="score-content">
          <div 
            class="score-number"
            :class="{ 'good-score': isGoodScore, 'bad-score': !isGoodScore }"
          >
            {{ displayScore }}
          </div>
          <div class="recommendation">
            {{ recommendation }}
          </div>
        </div>
      </div>

      <!-- 装备信息区域 -->
      <div class="equipment-info">
        <h3 class="section-title">装备信息</h3>
        <div class="info-content">
          <div v-if="equipment" class="equipment-details">
            <div class="basic-info">
              <div class="info-row">
                <span class="info-label">稀有度:</span>
                <span class="info-value">{{ equipment.rarity }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">类型:</span>
                <span class="info-value">{{ equipment.equipmentType }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">强化等级:</span>
                <span class="info-value">+{{ equipment.enhancementLevel }}</span>
              </div>
              <div class="info-row" v-if="equipment.setType">
                <span class="info-label">套装:</span>
                <span class="info-value">{{ equipment.setType }}</span>
              </div>
            </div>
            
            <div class="stats-info">
              <div class="stat-row main-stat">
                <span class="stat-label">{{ equipment.mainStat.statType }}:</span>
                <span class="stat-value">{{ formatStatValue(equipment.mainStat) }}</span>
              </div>
              <div 
                v-for="(stat, index) in equipment.subStats" 
                :key="index"
                class="stat-row sub-stat"
              >
                <span class="stat-label">{{ stat.statType }}:</span>
                <span class="stat-value">{{ formatStatValue(stat) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="no-equipment">
            暂无装备信息
          </div>
        </div>
      </div>
    </div>

    <div class="right-column">
      <!-- 图片预览区域 -->
      <div class="image-preview">
        <h3 class="section-title">截图预览</h3>
        <div class="preview-content">
          <img 
            v-if="screenshot" 
            :src="screenshot" 
            alt="装备截图"
            class="screenshot-image"
          />
          <div v-else class="no-image">
            暂无截图
          </div>
        </div>
      </div>

      <!-- 运行日志区域 -->
      <div class="log-display">
        <h3 class="section-title">运行日志</h3>
        <div class="log-content">
          <el-scrollbar height="300px">
            <div class="log-list">
              <div 
                v-for="(log, index) in logs" 
                :key="index"
                class="log-item"
              >
                {{ log }}
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Camera } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { invoke } from '@tauri-apps/api/core'
import type { Equipment, EquipmentScore, Stat, RecognitionResult } from '@/types'

// 响应式数据
const isRecognizing = ref(false)
const equipment = ref<Equipment | null>(null)
const score = ref<EquipmentScore | null>(null)
const screenshot = ref<string | null>(null)
const logs = ref<string[]>([])
const adbConnected = ref(false)

// 计算属性
const displayScore = computed(() => {
  if (!score.value) return '--/--'
  return `${Math.round(score.value.score)}/55`
})

const isGoodScore = computed(() => {
  return score.value?.isGoodForEnhancement ?? false
})

const recommendation = computed(() => {
  return score.value?.recommendation ?? '暂无建议'
})

// 检查ADB连接状态
const checkAdbConnection = async () => {
  try {
    const connected = await invoke<boolean>('test_adb_connection')
    adbConnected.value = connected

    // 发送状态更新事件到NavigationSidebar
    window.dispatchEvent(new CustomEvent('adb-status-update', {
      detail: { connected }
    }))

    return connected
  } catch (error) {
    adbConnected.value = false

    // 发送状态更新事件到NavigationSidebar
    window.dispatchEvent(new CustomEvent('adb-status-update', {
      detail: { connected: false }
    }))

    return false
  }
}

// 方法
const startRecognition = async () => {
  if (isRecognizing.value) return

  isRecognizing.value = true
  logs.value = [] // 清空之前的日志

  try {
    // 检查ADB连接
    logs.value.push(`[${new Date().toLocaleTimeString()}] 检查ADB连接...`)
    const connected = await checkAdbConnection()

    if (!connected) {
      logs.value.push(`[${new Date().toLocaleTimeString()}] ADB连接失败，请检查设备连接`)
      ElMessage.error('ADB连接失败，请检查设备连接')
      return
    }

    logs.value.push(`[${new Date().toLocaleTimeString()}] ADB连接成功，开始识别...`)
    const result = await invoke<RecognitionResult>('recognize_equipment')

    if (result.success) {
      equipment.value = result.equipment || null
      score.value = result.score || null
      screenshot.value = result.screenshot || null

      // 添加日志
      result.logs.forEach((log: string) => {
        logs.value.push(`[${new Date().toLocaleTimeString()}] ${log}`)
      })

      ElMessage.success('识别完成')
    } else {
      // 添加错误日志
      result.logs.forEach((log: string) => {
        logs.value.push(`[${new Date().toLocaleTimeString()}] ${log}`)
      })

      if (result.error) {
        logs.value.push(`[${new Date().toLocaleTimeString()}] 错误: ${result.error}`)
      }

      ElMessage.error(result.error || '识别失败')
    }
  } catch (error) {
    logs.value.push(`[${new Date().toLocaleTimeString()}] 识别失败: ${error}`)
    ElMessage.error('识别失败')
  } finally {
    isRecognizing.value = false
  }
}

const formatStatValue = (stat: Stat): string => {
  if (stat.isPercentage) {
    return `${stat.value}%`
  }
  return stat.value.toString()
}

// 初始化
onMounted(async () => {
  logs.value.push(`[${new Date().toLocaleTimeString()}] 装备助手已启动`)
  await checkAdbConnection()
})
</script>

<style scoped>
.equipment-assistant {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
}

.left-column,
.right-column {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.left-column {
  border-right: 1px solid #e4e7ed;
}

.control-panel,
.score-display,
.equipment-info,
.image-preview,
.log-display {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.recognition-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.score-content {
  text-align: center;
}

.score-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 8px;
}

.good-score {
  color: #67c23a;
}

.bad-score {
  color: #f56c6c;
}

.recommendation {
  font-size: 14px;
  color: #606266;
}

.equipment-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row,
.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label,
.stat-label {
  font-weight: 500;
  color: #606266;
}

.info-value,
.stat-value {
  color: #303133;
}

.main-stat {
  font-weight: 600;
  background-color: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  border: none;
}

.no-equipment,
.no-image {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.preview-content {
  text-align: center;
}

.screenshot-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-content {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 4px;
}

.log-item:last-child {
  margin-bottom: 0;
}
</style>
