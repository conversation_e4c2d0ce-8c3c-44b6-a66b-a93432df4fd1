{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 11859558468366090055, "deps": [[7312356825837975969, "crc32fast", false, 18125152622974776154], [7636735136738807108, "miniz_oxide", false, 8285917320830105053]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-4fe658a9a3da2f72\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}