<template>
  <div class="settings">
    <!-- 页面头部 -->
    <div class="settings-header">
      <h1 class="page-title">系统设置</h1>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :loading="isSaving"
          @click="saveSettings"
        >
          <el-icon><DocumentAdd /></el-icon>
          保存设置
        </el-button>
        <div class="status-indicator" :class="initStatus">
          <el-icon><CircleCheck v-if="initStatus === 'success'" /><Warning v-else /></el-icon>
          {{ getStatusText() }}
        </div>
      </div>
    </div>

    <!-- 设置区域 -->
    <div class="settings-content">
      <!-- ADB设置区域 -->
      <div class="settings-section">
        <h3 class="section-title">ADB连接设置</h3>
        <div class="section-content">
          <el-form :model="settings.adbSettings" label-width="120px">
            <el-form-item label="ADB端口">
              <el-input-number 
                v-model="settings.adbSettings.port"
                :min="1024"
                :max="65535"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="连接超时(秒)">
              <el-input-number 
                v-model="settings.adbSettings.timeoutSeconds"
                :min="5"
                :max="60"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="自动连接">
              <el-switch v-model="settings.adbSettings.autoConnect" />
            </el-form-item>
            <el-form-item label="连接状态">
              <div class="connection-status">
                <el-tag :type="connectionStatusType">{{ connectionStatusText }}</el-tag>
                <el-button 
                  size="small" 
                  :loading="isConnecting"
                  @click="testConnection"
                >
                  测试连接
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 热键设置区域 -->
      <div class="settings-section">
        <h3 class="section-title">热键设置</h3>
        <div class="section-content">
          <el-form :model="settings.hotkeySettings" label-width="120px">
            <el-form-item label="启用热键">
              <el-switch v-model="settings.hotkeySettings.enabled" />
            </el-form-item>
            <el-form-item label="热键">
              <el-input 
                v-model="settings.hotkeySettings.key"
                placeholder="如: F1"
                :disabled="!settings.hotkeySettings.enabled"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- OCR设置区域 -->
      <div class="settings-section">
        <h3 class="section-title">OCR识别设置</h3>
        <div class="section-content">
          <el-form :model="settings.ocrSettings" label-width="150px">
            <el-form-item label="页面识别阈值">
              <el-slider 
                v-model="settings.ocrSettings.pageDetectionThreshold"
                :min="0.1"
                :max="1.0"
                :step="0.1"
                show-input
              />
            </el-form-item>
            <el-form-item label="装备检测阈值">
              <el-slider 
                v-model="settings.ocrSettings.equipmentDetectionThreshold"
                :min="0.1"
                :max="1.0"
                :step="0.1"
                show-input
              />
            </el-form-item>
            <el-form-item label="边框阈值">
              <el-slider 
                v-model="settings.ocrSettings.borderThreshold"
                :min="0.1"
                :max="1.0"
                :step="0.1"
                show-input
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 装备分数设置区域 -->
      <div class="settings-section">
        <h3 class="section-title">装备分数标准</h3>
        <div class="section-content">
          <div class="score-tables">
            <div class="score-table">
              <h4>左三装备分数标准</h4>
              <el-table :data="leftThreeTableData" size="small">
                <el-table-column prop="level" label="强化等级" width="100" />
                <el-table-column prop="score" label="分数标准">
                  <template #default="{ $index }">
                    <el-input-number 
                      v-model="settings.scoreSettings.leftThreeStandards[$index]"
                      :min="0"
                      :max="100"
                      size="small"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="score-table">
              <h4>右三装备分数标准</h4>
              <el-table :data="rightThreeTableData" size="small">
                <el-table-column prop="level" label="强化等级" width="100" />
                <el-table-column prop="score" label="分数标准">
                  <template #default="{ $index }">
                    <el-input-number 
                      v-model="settings.scoreSettings.rightThreeStandards[$index]"
                      :min="0"
                      :max="100"
                      size="small"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <div class="score-tables">
            <div class="score-table">
              <h4>速度属性分数标准</h4>
              <el-table :data="speedTableData" size="small">
                <el-table-column prop="level" label="强化等级" width="100" />
                <el-table-column prop="score" label="速度标准">
                  <template #default="{ $index }">
                    <el-input-number 
                      v-model="settings.scoreSettings.speedStandards[$index]"
                      :min="0"
                      :max="20"
                      size="small"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="score-table">
              <h4>重铸界面分数标准</h4>
              <el-table :data="recastTableData" size="small">
                <el-table-column prop="type" label="装备类型" width="120" />
                <el-table-column prop="score" label="分数标准">
                  <template #default="{ $index }">
                    <el-input-number 
                      v-model="settings.scoreSettings.recastStandards[$index]"
                      :min="0"
                      :max="100"
                      size="small"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { DocumentAdd, CircleCheck, Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { invoke } from '@tauri-apps/api/core'
import type { AppConfig } from '@/types'
import { ConnectionStatus } from '@/types'

// 响应式数据
const isSaving = ref(false)
const isConnecting = ref(false)
const initStatus = ref<'success' | 'warning'>('warning')
const connectionStatus = ref<ConnectionStatus>('disconnected' as ConnectionStatus)

// 设置数据
const settings = ref<AppConfig>({
  adbSettings: {
    port: 16384,
    timeoutSeconds: 30,
    autoConnect: false
  },
  hotkeySettings: {
    enabled: false,
    key: 'F1'
  },
  ocrSettings: {
    pageDetectionThreshold: 0.8,
    equipmentDetectionThreshold: 0.7,
    borderThreshold: 0.6
  },
  scoreSettings: {
    leftThreeStandards: [21, 28, 35, 42, 49, 55],
    rightThreeStandards: [19, 26, 33, 40, 47, 53],
    speedStandards: [3, 6, 10, 12, 12, 14],
    recastStandards: [67, 65]
  }
})

// 计算属性
const connectionStatusType = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'success'
    case 'connecting': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'error': return '连接失败'
    default: return '未连接'
  }
})

// 表格数据
const leftThreeTableData = [
  { level: '+0-2' }, { level: '+3-5' }, { level: '+6-8' },
  { level: '+9-11' }, { level: '+12-14' }, { level: '+15' }
]

const rightThreeTableData = [
  { level: '+0-2' }, { level: '+3-5' }, { level: '+6-8' },
  { level: '+9-11' }, { level: '+12-14' }, { level: '+15' }
]

const speedTableData = [
  { level: '+0-2' }, { level: '+3-5' }, { level: '+6-8' },
  { level: '+9-11' }, { level: '+12-14' }, { level: '+15' }
]

const recastTableData = [
  { type: '武器/头盔/铠甲' },
  { type: '项链/戒指/鞋子' }
]

// 方法
const getStatusText = () => {
  return initStatus.value === 'success' ? '初始化完成' : '等待初始化'
}

const saveSettings = async () => {
  if (isSaving.value) return

  isSaving.value = true
  try {
    await invoke('save_config', { config: settings.value })
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error(`设置保存失败: ${error}`)
  } finally {
    isSaving.value = false
  }
}

const testConnection = async () => {
  if (isConnecting.value) return

  isConnecting.value = true
  connectionStatus.value = 'connecting' as ConnectionStatus

  try {
    const result = await invoke<boolean>('test_adb_connection')

    if (result) {
      connectionStatus.value = 'connected' as ConnectionStatus
      ElMessage.success('ADB连接测试成功')

      // 获取设备信息
      try {
        const deviceInfo = await invoke<string>('get_device_info')
        ElMessage.info(`设备信息: ${deviceInfo}`)
      } catch (error) {
        console.warn('获取设备信息失败:', error)
      }
    } else {
      connectionStatus.value = 'error' as ConnectionStatus
      ElMessage.error('ADB连接测试失败')
    }
  } catch (error) {
    connectionStatus.value = 'error' as ConnectionStatus
    ElMessage.error(`ADB连接测试失败: ${error}`)
  } finally {
    isConnecting.value = false
  }
}

const loadSettings = async () => {
  try {
    const config = await invoke<AppConfig>('load_config')
    settings.value = config
    initStatus.value = 'success'
    ElMessage.success('设置加载成功')
  } catch (error) {
    ElMessage.error(`加载设置失败: ${error}`)
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.status-indicator.success {
  color: #67c23a;
}

.status-indicator.warning {
  color: #e6a23c;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.score-table h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

@media (max-width: 1200px) {
  .score-tables {
    grid-template-columns: 1fr;
  }
}
</style>
