{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 9237431349354673483, "deps": [[654232091421095663, "tauri_utils", false, 6312412895848897844], [3150220818285335163, "url", false, 12542923243119232473], [4143744114649553716, "raw_window_handle", false, 13938569619552517521], [7606335748176206944, "dpi", false, 3448183060085483610], [8569119365930580996, "serde_json", false, 13470422440041892068], [9010263965687315507, "http", false, 16749436415575297115], [9689903380558560274, "serde", false, 13238231853154323401], [10806645703491011684, "thiserror", false, 7309883229814674106], [12943761728066819757, "build_script_build", false, 13793681959163289186], [14585479307175734061, "windows", false, 9741366053200353985], [16727543399706004146, "cookie", false, 6078882761362717207]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-7e06ac192b24110c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}