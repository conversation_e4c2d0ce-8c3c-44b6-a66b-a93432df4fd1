{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 9024908729764251435, "deps": [[40386456601120721, "percent_encoding", false, 7507067352408640895], [654232091421095663, "tauri_utils", false, 6312412895848897844], [1200537532907108615, "url<PERSON><PERSON>n", false, 9881324285225764361], [1967864351173319501, "muda", false, 875827329437422961], [2013030631243296465, "webview2_com", false, 3033330217559763731], [3150220818285335163, "url", false, 12542923243119232473], [3331586631144870129, "getrandom", false, 7010357797991220996], [4143744114649553716, "raw_window_handle", false, 13938569619552517521], [4919829919303820331, "serialize_to_javascript", false, 6830710424001322535], [5986029879202738730, "log", false, 2334386541417879784], [8569119365930580996, "serde_json", false, 13470422440041892068], [9010263965687315507, "http", false, 16749436415575297115], [9689903380558560274, "serde", false, 13238231853154323401], [10229185211513642314, "mime", false, 13941710587972742405], [10806645703491011684, "thiserror", false, 7309883229814674106], [11989259058781683633, "dunce", false, 13687696130865513278], [12092653563678505622, "build_script_build", false, 2020648326448671818], [12304025191202589669, "tauri_runtime_wry", false, 9474626348234249298], [12565293087094287914, "window_vibrancy", false, 14267209787902174818], [12943761728066819757, "tauri_runtime", false, 9315366099735250839], [12944427623413450645, "tokio", false, 6724755346568104041], [12986574360607194341, "serde_repr", false, 14324535416803695765], [13077543566650298139, "heck", false, 4270090852814317102], [13405681745520956630, "tauri_macros", false, 6389389183109304598], [13625485746686963219, "anyhow", false, 11743789584046627553], [14585479307175734061, "windows", false, 9741366053200353985], [16928111194414003569, "dirs", false, 1146849732613989772], [17155886227862585100, "glob", false, 14140733676001939860]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-d4303d54267276b2\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}