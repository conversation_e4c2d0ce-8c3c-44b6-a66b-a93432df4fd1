use anyhow::{anyhow, Result};
use image::{DynamicImage, Rgb, RgbImage, GenericImageView};
use std::path::{Path, PathBuf};
use std::process::Command;
use crate::equipment::types::{PageType, OCRResult};

pub struct OCREngine {
    paddle_ocr_path: PathBuf,
}

impl OCREngine {
    pub fn new() -> Self {
        Self {
            paddle_ocr_path: PathBuf::from("src-tauri/PaddleOCR-json"),
        }
    }

    pub fn detect_page_type(&self, image_path: &Path) -> Result<PageType> {
        // 加载图像
        let img = image::open(image_path)?;
        
        // 确保图像尺寸为1280x720
        let (width, height) = img.dimensions();
        if width != 1280 || height != 720 {
            return Err(anyhow!("图像尺寸不正确，期望1280x720，实际{}x{}", width, height));
        }

        // 截取标题区域进行OCR识别
        let title_region = self.extract_title_region(&img);
        let title_path = PathBuf::from("src-tauri/screenshot/title_region.png");
        title_region.save(&title_path)?;

        // 对标题区域进行OCR识别
        let ocr_results = self.extract_text(&title_path)?;
        
        // 根据识别结果判断页面类型
        for result in &ocr_results {
            let text = result.text.trim();
            if text.contains("强化装备") {
                return Ok(PageType::Enhancement);
            } else if text.contains("背包") || text.contains("钢铁工坊") {
                // 需要进一步识别背包和重铸界面
                return self.distinguish_backpack_recast(&img);
            }
        }

        Ok(PageType::Other)
    }

    fn extract_title_region(&self, image: &DynamicImage) -> DynamicImage {
        // 截取区域：[y_min=15, y_max=50, x_min=60, x_max=175]
        let cropped = image.crop_imm(60, 15, 115, 35);
        cropped
    }

    fn distinguish_backpack_recast(&self, image: &DynamicImage) -> Result<PageType> {
        // 截取标志区域：[y_min=160, y_max=215, x_min=25, x_max=105]
        let sign_region = image.crop_imm(25, 160, 80, 55);
        let sign_path = PathBuf::from("src-tauri/screenshot/sign_region.png");
        sign_region.save(&sign_path)?;

        // 与标志图像进行对比
        let backpack_similarity = self.compare_with_sign(&sign_region, "Backpack_sign.png")?;
        let recast_similarity = self.compare_with_sign(&sign_region, "Recast_sign.png")?;

        if backpack_similarity > recast_similarity && backpack_similarity > 0.7 {
            Ok(PageType::Backpack)
        } else if recast_similarity > 0.7 {
            Ok(PageType::Recast)
        } else {
            Ok(PageType::Other)
        }
    }

    fn compare_with_sign(&self, region: &DynamicImage, sign_filename: &str) -> Result<f32> {
        let sign_path = PathBuf::from("src-tauri/page_sign").join(sign_filename);
        
        if !sign_path.exists() {
            return Err(anyhow!("标志文件不存在: {:?}", sign_path));
        }

        let sign_image = image::open(&sign_path)?;
        
        // 简单的像素相似度比较
        let similarity = self.calculate_image_similarity(region, &sign_image);
        Ok(similarity)
    }

    fn calculate_image_similarity(&self, img1: &DynamicImage, img2: &DynamicImage) -> f32 {
        let (w1, h1) = img1.dimensions();
        let (w2, h2) = img2.dimensions();
        
        if w1 != w2 || h1 != h2 {
            return 0.0;
        }

        let rgb1 = img1.to_rgb8();
        let rgb2 = img2.to_rgb8();
        
        let mut total_diff = 0u64;
        let total_pixels = (w1 * h1) as u64;
        
        for y in 0..h1 {
            for x in 0..w1 {
                let pixel1 = rgb1.get_pixel(x, y);
                let pixel2 = rgb2.get_pixel(x, y);
                
                let diff = ((pixel1[0] as i32 - pixel2[0] as i32).abs() +
                           (pixel1[1] as i32 - pixel2[1] as i32).abs() +
                           (pixel1[2] as i32 - pixel2[2] as i32).abs()) as u64;
                
                total_diff += diff;
            }
        }
        
        let max_diff = total_pixels * 255 * 3;
        let similarity = 1.0 - (total_diff as f32 / max_diff as f32);
        similarity
    }

    pub fn process_equipment_image(&self, image_path: &Path, page_type: PageType) -> Result<PathBuf> {
        let img = image::open(image_path)?;
        
        let processed_img = match page_type {
            PageType::Backpack => self.process_backpack_image(&img),
            PageType::Enhancement => self.process_enhancement_image(&img),
            PageType::Recast => self.process_recast_image(&img),
            _ => return Err(anyhow!("不支持的页面类型")),
        };

        let output_path = PathBuf::from("src-tauri/screenshot/processed_equipment.png");
        processed_img.save(&output_path)?;
        Ok(output_path)
    }

    fn process_backpack_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取装备信息区域：[y_min=165, y_max=585, x_min=875, x_max=1225]
        let equipment_region = image.crop_imm(875, 165, 350, 420);
        
        // 创建黑色背景
        let mut processed = RgbImage::new(350, 420);
        for pixel in processed.pixels_mut() {
            *pixel = Rgb([0, 0, 0]);
        }

        // 保留四个关键区域
        let regions = [
            (45, 0, 45, 35),   // 稀有度区域
            (90, 15, 80, 32),  // 类型区域
            (0, 155, 350, 180), // 属性区域
            (40, 385, 200, 35), // 套装区域
        ];

        let equipment_rgb = equipment_region.to_rgb8();
        
        for (x_offset, y_offset, width, height) in regions {
            for y in 0..height {
                for x in 0..width {
                    if x_offset + x < 350 && y_offset + y < 420 {
                        if let Some(pixel) = equipment_rgb.get_pixel_checked(x_offset + x, y_offset + y) {
                            processed.put_pixel(x_offset + x, y_offset + y, *pixel);
                        }
                    }
                }
            }
        }

        DynamicImage::ImageRgb8(processed)
    }

    fn process_enhancement_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取装备信息区域：[y_min=80, y_max=480, x_min=30, x_max=370]
        let equipment_region = image.crop_imm(30, 80, 340, 400);
        
        // 创建黑色背景
        let mut processed = RgbImage::new(340, 400);
        for pixel in processed.pixels_mut() {
            *pixel = Rgb([0, 0, 0]);
        }

        // 保留四个关键区域
        let regions = [
            (60, 0, 45, 35),    // 稀有度区域
            (105, 17, 75, 23),  // 类型区域
            (0, 135, 340, 215), // 属性区域
            (43, 365, 200, 35), // 套装区域
        ];

        let equipment_rgb = equipment_region.to_rgb8();
        
        for (x_offset, y_offset, width, height) in regions {
            for y in 0..height {
                for x in 0..width {
                    if x_offset + x < 340 && y_offset + y < 400 {
                        if let Some(pixel) = equipment_rgb.get_pixel_checked(x_offset + x, y_offset + y) {
                            processed.put_pixel(x_offset + x, y_offset + y, *pixel);
                        }
                    }
                }
            }
        }

        DynamicImage::ImageRgb8(processed)
    }

    fn process_recast_image(&self, image: &DynamicImage) -> DynamicImage {
        // 截取重铸区域：[y_min=490, y_max=625, x_min=280, x_max=770]
        let recast_region = image.crop_imm(280, 490, 490, 135);
        
        // 涂黑指定区域：[y_min=0, y_max=135, x_min=370, x_max=430]
        let mut recast_rgb = recast_region.to_rgb8();
        for y in 0..135 {
            for x in 370..430 {
                if x < 490 {
                    recast_rgb.put_pixel(x, y, Rgb([0, 0, 0]));
                }
            }
        }

        DynamicImage::ImageRgb8(recast_rgb)
    }

    pub fn extract_text(&self, image_path: &Path) -> Result<Vec<OCRResult>> {
        let output = Command::new(&self.paddle_ocr_path.join("PaddleOCR-json.exe"))
            .arg("--det_limit_type")
            .arg("max")
            .arg("--det_limit_side_len")
            .arg("96")
            .arg(image_path)
            .output()?;

        if !output.status.success() {
            return Err(anyhow!("OCR识别失败: {}", String::from_utf8_lossy(&output.stderr)));
        }

        let output_str = String::from_utf8(output.stdout)?;
        let json_value: serde_json::Value = serde_json::from_str(&output_str)?;

        let mut results = Vec::new();
        
        if let Some(data) = json_value.get("data") {
            if let Some(array) = data.as_array() {
                for item in array {
                    if let (Some(text), Some(score), Some(box_data)) = (
                        item.get("text").and_then(|v| v.as_str()),
                        item.get("score").and_then(|v| v.as_f64()),
                        item.get("box").and_then(|v| v.as_array())
                    ) {
                        if box_data.len() >= 4 {
                            let bbox = [
                                box_data[0].as_f64().unwrap_or(0.0) as f32,
                                box_data[1].as_f64().unwrap_or(0.0) as f32,
                                box_data[2].as_f64().unwrap_or(0.0) as f32,
                                box_data[3].as_f64().unwrap_or(0.0) as f32,
                            ];
                            
                            results.push(OCRResult {
                                text: text.to_string(),
                                confidence: score as f32,
                                bbox,
                            });
                        }
                    }
                }
            }
        }

        Ok(results)
    }
}
