pub mod config;
mod adb;
mod ocr;
mod equipment;
mod commands;

use std::sync::Mutex;
use commands::AppState;
use config::ConfigManager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化配置
    let config = ConfigManager::load_config().unwrap_or_default();
    let app_state = AppState {
        config: Mutex::new(config),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            commands::load_config,
            commands::save_config,
            commands::test_adb_connection,
            commands::get_device_info,
            commands::recognize_equipment,
            commands::check_screen_resolution
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
