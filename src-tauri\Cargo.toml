[package]
name = "e7gearocr"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "e7gearocr_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[[bin]]
name = "generate_config"
path = "src/bin/generate_config.rs"

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
anyhow = "1.0"
thiserror = "1.0"
image = "0.25"
regex = "1.10"
# adb_client = { version = "0.8", default-features = false }
base64 = "0.22"

