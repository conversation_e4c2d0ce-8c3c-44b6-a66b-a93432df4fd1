#[cfg(test)]
mod tests {
    // use super::*;
    use crate::equipment::types::*;
    use crate::equipment::parser::EquipmentParser;
    use crate::equipment::scorer::EquipmentScorer;
    use crate::config::ScoreConfig;

    fn create_test_ocr_results() -> Vec<OCRResult> {
        vec![
            OCRResult {
                text: "传说武器".to_string(),
                confidence: 0.95,
                bbox: [0.0, 0.0, 100.0, 30.0],
            },
            OCRResult {
                text: "+15".to_string(),
                confidence: 0.90,
                bbox: [0.0, 35.0, 50.0, 60.0],
            },
            OCRResult {
                text: "攻击力".to_string(),
                confidence: 0.85,
                bbox: [0.0, 100.0, 60.0, 120.0],
            },
            OCRResult {
                text: "525".to_string(),
                confidence: 0.88,
                bbox: [70.0, 100.0, 120.0, 120.0],
            },
            OCRResult {
                text: "攻击力%".to_string(),
                confidence: 0.85,
                bbox: [0.0, 130.0, 70.0, 150.0],
            },
            OCRResult {
                text: "8%".to_string(),
                confidence: 0.88,
                bbox: [80.0, 130.0, 120.0, 150.0],
            },
            OCRResult {
                text: "暴击率".to_string(),
                confidence: 0.85,
                bbox: [0.0, 160.0, 60.0, 180.0],
            },
            OCRResult {
                text: "5%".to_string(),
                confidence: 0.88,
                bbox: [70.0, 160.0, 100.0, 180.0],
            },
            OCRResult {
                text: "暴击伤害".to_string(),
                confidence: 0.85,
                bbox: [0.0, 190.0, 70.0, 210.0],
            },
            OCRResult {
                text: "7%".to_string(),
                confidence: 0.88,
                bbox: [80.0, 190.0, 110.0, 210.0],
            },
            OCRResult {
                text: "速度".to_string(),
                confidence: 0.85,
                bbox: [0.0, 220.0, 40.0, 240.0],
            },
            OCRResult {
                text: "4".to_string(),
                confidence: 0.88,
                bbox: [50.0, 220.0, 70.0, 240.0],
            },
        ]
    }

    #[test]
    fn test_equipment_parser() {
        let parser = EquipmentParser::new();
        let ocr_results = create_test_ocr_results();
        
        let equipment = parser.parse_equipment(ocr_results, PageType::Backpack).unwrap();
        
        assert_eq!(equipment.rarity, Rarity::Legendary);
        assert_eq!(equipment.equipment_type, EquipmentType::Weapon);
        assert_eq!(equipment.enhancement_level, 15);
        assert!(!equipment.is_recast);
        
        // 验证主属性
        assert_eq!(equipment.main_stat.stat_type, StatType::Attack);
        assert_eq!(equipment.main_stat.value, 525.0);
        assert!(!equipment.main_stat.is_percentage);
        
        // 验证副属性
        assert_eq!(equipment.sub_stats.len(), 4);
        
        let attack_percent = equipment.sub_stats.iter()
            .find(|s| s.stat_type == StatType::AttackPercent)
            .unwrap();
        assert_eq!(attack_percent.value, 8.0);
        assert!(attack_percent.is_percentage);
        
        let crit_rate = equipment.sub_stats.iter()
            .find(|s| s.stat_type == StatType::CriticalRate)
            .unwrap();
        assert_eq!(crit_rate.value, 5.0);
        
        let crit_damage = equipment.sub_stats.iter()
            .find(|s| s.stat_type == StatType::CriticalDamage)
            .unwrap();
        assert_eq!(crit_damage.value, 7.0);
        
        let speed = equipment.sub_stats.iter()
            .find(|s| s.stat_type == StatType::Speed)
            .unwrap();
        assert_eq!(speed.value, 4.0);
    }

    #[test]
    fn test_equipment_scorer() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        
        let equipment = Equipment {
            rarity: Rarity::Legendary,
            equipment_type: EquipmentType::Weapon,
            enhancement_level: 15,
            set_type: None,
            main_stat: Stat::new(StatType::Attack, 525.0),
            sub_stats: vec![
                Stat::new(StatType::AttackPercent, 8.0),
                Stat::new(StatType::CriticalRate, 5.0),
                Stat::new(StatType::CriticalDamage, 7.0),
                Stat::new(StatType::Speed, 4.0),
            ],
            is_recast: false,
        };
        
        let score_result = scorer.calculate_score(&equipment);
        
        // 验证分数计算
        // 8 + 5*(9/6) + 7*(9/8) + 4*2 = 8 + 7.5 + 7.875 + 8 = 31.375
        assert!((score_result.score - 31.375).abs() < 0.1);
        
        // 对于+15的武器，标准是55，31.375 < 55，所以不建议强化
        assert_eq!(score_result.recommendation, "不建议强化");
        assert!(!score_result.is_good_for_enhancement);
    }

    #[test]
    fn test_high_score_equipment() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        
        let equipment = Equipment {
            rarity: Rarity::Legendary,
            equipment_type: EquipmentType::Weapon,
            enhancement_level: 15,
            set_type: None,
            main_stat: Stat::new(StatType::Attack, 525.0),
            sub_stats: vec![
                Stat::new(StatType::AttackPercent, 20.0),
                Stat::new(StatType::CriticalRate, 12.0),
                Stat::new(StatType::CriticalDamage, 16.0),
                Stat::new(StatType::Speed, 8.0),
            ],
            is_recast: false,
        };
        
        let score_result = scorer.calculate_score(&equipment);
        
        // 验证高分装备
        // 20 + 12*(9/6) + 16*(9/8) + 8*2 = 20 + 18 + 18 + 16 = 72
        assert!((score_result.score - 72.0).abs() < 0.1);
        
        // 72 > 55，所以建议强化
        assert_eq!(score_result.recommendation, "建议强化");
        assert!(score_result.is_good_for_enhancement);
    }

    #[test]
    fn test_speed_equipment() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        
        let equipment = Equipment {
            rarity: Rarity::Legendary,
            equipment_type: EquipmentType::Weapon,
            enhancement_level: 15,
            set_type: None,
            main_stat: Stat::new(StatType::Attack, 525.0),
            sub_stats: vec![
                Stat::new(StatType::AttackPercent, 5.0),
                Stat::new(StatType::CriticalRate, 3.0),
                Stat::new(StatType::CriticalDamage, 4.0),
                Stat::new(StatType::Speed, 15.0), // 高速度
            ],
            is_recast: false,
        };
        
        let score_result = scorer.calculate_score(&equipment);
        
        // 验证速度装备识别
        // 注意：这些是私有方法，我们通过公共接口测试
        assert_eq!(score_result.recommendation, "可作为赌速度装备");
    }

    #[test]
    fn test_recast_equipment() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        
        let equipment = Equipment {
            rarity: Rarity::Legendary,
            equipment_type: EquipmentType::Weapon,
            enhancement_level: 15,
            set_type: None,
            main_stat: Stat::new(StatType::Attack, 525.0),
            sub_stats: vec![
                Stat::new(StatType::AttackPercent, 25.0),
                Stat::new(StatType::CriticalRate, 15.0),
                Stat::new(StatType::CriticalDamage, 20.0),
                Stat::new(StatType::Speed, 10.0),
            ],
            is_recast: true,
        };
        
        let score_result = scorer.calculate_score(&equipment);
        
        // 验证重铸装备评分
        // 25 + 15*(9/6) + 20*(9/8) + 10*2 = 25 + 22.5 + 22.5 + 20 = 90
        assert!((score_result.score - 90.0).abs() < 0.1);
        
        // 90 > 67 (左三标准)，所以建议重铸
        assert_eq!(score_result.recommendation, "建议重铸");
        assert!(score_result.is_good_for_enhancement);
    }

    #[test]
    fn test_equipment_type_classification() {
        assert!(EquipmentType::Weapon.is_left_three());
        assert!(EquipmentType::Helmet.is_left_three());
        assert!(EquipmentType::Armor.is_left_three());
        
        assert!(EquipmentType::Necklace.is_right_three());
        assert!(EquipmentType::Ring.is_right_three());
        assert!(EquipmentType::Boots.is_right_three());
        
        assert!(!EquipmentType::Weapon.is_right_three());
        assert!(!EquipmentType::Necklace.is_left_three());
    }

    #[test]
    fn test_stat_type_percentage() {
        assert!(StatType::AttackPercent.is_percentage());
        assert!(StatType::HealthPercent.is_percentage());
        assert!(StatType::DefensePercent.is_percentage());
        assert!(StatType::CriticalRate.is_percentage());
        assert!(StatType::CriticalDamage.is_percentage());
        assert!(StatType::EffectHit.is_percentage());
        assert!(StatType::EffectResistance.is_percentage());
        
        assert!(!StatType::Attack.is_percentage());
        assert!(!StatType::Health.is_percentage());
        assert!(!StatType::Defense.is_percentage());
        assert!(!StatType::Speed.is_percentage());
    }
}
