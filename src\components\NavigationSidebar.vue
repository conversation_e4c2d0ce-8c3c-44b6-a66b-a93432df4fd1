<template>
  <div class="navigation-sidebar">
    <div class="nav-header">
      <h2>E7装备助手</h2>
    </div>
    
    <nav class="nav-menu">
      <div
        v-for="item in navItems"
        :key="item.id"
        class="nav-item"
        :class="{ active: currentRoute === item.route }"
        @click="navigateTo(item.route)"
      >
        <el-icon class="nav-icon">
          <Camera v-if="item.icon === 'camera'" />
          <Setting v-if="item.icon === 'settings'" />
        </el-icon>
        <span class="nav-label">{{ item.name }}</span>
      </div>
    </nav>

    <!-- ADB连接状态显示 -->
    <div class="adb-status">
      <div class="status-item" :class="{ connected: adbConnected, disconnected: !adbConnected }">
        <el-icon class="status-icon">
          <Cellphone />
        </el-icon>
        <span class="status-text">
          {{ adbConnected ? 'ADB已连接' : 'ADB未连接' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Camera, Setting, Cellphone } from '@element-plus/icons-vue'
import { invoke } from '@tauri-apps/api/core'

interface NavItem {
  id: string
  name: string
  icon: string
  route: string
}

const router = useRouter()
const route = useRoute()

const currentRoute = computed(() => route.path)
const adbConnected = ref(false)

// ADB状态检查
const checkAdbStatus = async () => {
  try {
    const connected = await invoke<boolean>('test_adb_connection')
    adbConnected.value = connected
  } catch (error) {
    adbConnected.value = false
  }
}

const navItems: NavItem[] = [
  {
    id: 'equipment',
    name: '装备助手',
    icon: 'camera',
    route: '/equipment'
  },
  {
    id: 'settings',
    name: '参数配置',
    icon: 'settings',
    route: '/settings'
  }
]

const navigateTo = (path: string) => {
  router.push(path)
}

// 监听来自其他组件的ADB状态更新
const handleAdbStatusUpdate = (event: CustomEvent) => {
  adbConnected.value = event.detail.connected
}

// 初始化和清理
onMounted(async () => {
  await checkAdbStatus()
  // 监听自定义事件
  window.addEventListener('adb-status-update', handleAdbStatusUpdate as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('adb-status-update', handleAdbStatusUpdate as EventListener)
})
</script>

<style scoped>
.navigation-sidebar {
  width: 200px;
  height: 100vh;
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.nav-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.nav-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.nav-menu {
  flex: 1;
  padding: 16px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
}

.nav-item:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

.nav-item.active {
  background-color: #409eff;
  color: white;
  position: relative;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #66b1ff;
}

.nav-icon {
  margin-right: 8px;
  font-size: 18px;
}

.nav-label {
  font-size: 14px;
  font-weight: 500;
}

/* ADB状态样式 */
.adb-status {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.status-item.connected {
  background-color: #f0f9ff;
  color: #16a34a;
  border: 1px solid #dcfce7;
}

.status-item.disconnected {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}
</style>
