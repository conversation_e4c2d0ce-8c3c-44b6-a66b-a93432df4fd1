<script setup lang="ts">
import { onMounted } from "vue";
import NavigationSidebar from '@/components/NavigationSidebar.vue'

onMounted(() => {
  document.body.addEventListener("contextmenu", (e) => {
    if ((e.target as HTMLElement)?.tagName !== "INPUT") e.preventDefault();
  });
});
</script>

<template>
  <div class="app">
    <NavigationSidebar />
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #f5f7fa;
}

.app {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow-y: auto;
}
</style>