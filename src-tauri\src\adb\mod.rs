use anyhow::{anyhow, Result};
use std::path::PathBuf;
use std::process::Command;
use std::time::Duration;
use tokio::time::timeout;

pub struct ADBManager {
    port: u16,
    timeout_duration: Duration,
}

impl ADBManager {
    pub fn new(port: u16, timeout_seconds: u64) -> Self {
        Self {
            port,
            timeout_duration: Duration::from_secs(timeout_seconds),
        }
    }

    pub async fn test_connection(&self) -> Result<bool> {
        let future = self.check_devices();
        match timeout(self.timeout_duration, future).await {
            Ok(result) => result.map(|devices| !devices.is_empty()),
            Err(_) => Err(anyhow!("连接超时")),
        }
    }

    pub async fn take_screenshot(&self) -> Result<PathBuf> {
        // 确保目录存在
        let local_path = PathBuf::from("src-tauri/screenshot/screenshot.png");
        if let Some(parent) = local_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // 执行截图命令
        let output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["shell", "screencap", "-p", "/sdcard/screenshot.png"])
            .output()?;

        if !output.status.success() {
            return Err(anyhow!("截图命令执行失败: {}", String::from_utf8_lossy(&output.stderr)));
        }

        // 下载截图文件
        let output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["pull", "/sdcard/screenshot.png", local_path.to_str().unwrap()])
            .output()?;

        if !output.status.success() {
            return Err(anyhow!("截图文件下载失败: {}", String::from_utf8_lossy(&output.stderr)));
        }

        // 验证文件是否存在
        if !local_path.exists() {
            return Err(anyhow!("截图文件下载失败"));
        }

        Ok(local_path)
    }

    async fn check_devices(&self) -> Result<Vec<String>> {
        let output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["devices"])
            .output()?;

        if !output.status.success() {
            return Err(anyhow!("获取设备列表失败: {}", String::from_utf8_lossy(&output.stderr)));
        }

        let output_str = String::from_utf8(output.stdout)?;
        let devices: Vec<String> = output_str
            .lines()
            .skip(1) // 跳过标题行
            .filter_map(|line| {
                if line.contains("device") && !line.contains("offline") {
                    Some(line.split_whitespace().next().unwrap_or("").to_string())
                } else {
                    None
                }
            })
            .collect();

        Ok(devices)
    }

    pub async fn get_device_info(&self) -> Result<String> {
        // 获取设备型号
        let model_output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["shell", "getprop", "ro.product.model"])
            .output()?;

        // 获取Android版本
        let version_output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["shell", "getprop", "ro.build.version.release"])
            .output()?;

        if !model_output.status.success() || !version_output.status.success() {
            return Err(anyhow!("获取设备信息失败"));
        }

        let model = String::from_utf8(model_output.stdout)?.trim().to_string();
        let version = String::from_utf8(version_output.stdout)?.trim().to_string();

        Ok(format!("设备型号: {}, Android版本: {}", model, version))
    }

    pub async fn check_screen_resolution(&self) -> Result<(u32, u32)> {
        let output = Command::new("adb")
            .args(["-P", &self.port.to_string()])
            .args(["shell", "wm", "size"])
            .output()?;

        if !output.status.success() {
            return Err(anyhow!("获取屏幕分辨率失败: {}", String::from_utf8_lossy(&output.stderr)));
        }

        let output_str = String::from_utf8(output.stdout)?;

        // 解析输出，格式通常是 "Physical size: 1280x720"
        if let Some(size_part) = output_str.split(':').nth(1) {
            let size_str = size_part.trim();
            if let Some((width_str, height_str)) = size_str.split_once('x') {
                let width: u32 = width_str.parse()?;
                let height: u32 = height_str.parse()?;
                return Ok((width, height));
            }
        }

        Err(anyhow!("无法解析屏幕分辨率"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_adb_manager_creation() {
        let adb = ADBManager::new(16384, 30);
        assert_eq!(adb.port, 16384);
        assert_eq!(adb.timeout_duration, Duration::from_secs(30));
    }

    #[tokio::test]
    async fn test_check_devices() {
        let adb = ADBManager::new(16384, 30);

        // 这个测试需要实际的ADB连接，所以我们只测试方法存在
        match adb.check_devices().await {
            Ok(devices) => println!("找到设备: {:?}", devices),
            Err(e) => println!("检查设备失败: {}", e),
        }
    }

    #[test]
    fn test_adb_manager_new() {
        let adb = ADBManager::new(5555, 60);
        assert_eq!(adb.port, 5555);
        assert_eq!(adb.timeout_duration, Duration::from_secs(60));
    }
}
