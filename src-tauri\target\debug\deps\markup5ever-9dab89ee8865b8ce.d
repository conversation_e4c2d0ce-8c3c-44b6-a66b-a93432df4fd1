D:\vscode\E7gearOcr\src-tauri\target\debug\deps\markup5ever-9dab89ee8865b8ce.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

D:\vscode\E7gearOcr\src-tauri\target\debug\deps\libmarkup5ever-9dab89ee8865b8ce.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

D:\vscode\E7gearOcr\src-tauri\target\debug\deps\libmarkup5ever-9dab89ee8865b8ce.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs:
D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs:
D:\vscode\E7gearOcr\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs:

# env-dep:OUT_DIR=D:\\vscode\\E7gearOcr\\src-tauri\\target\\debug\\build\\markup5ever-506fc92b0899210e\\out
