use crate::equipment::types::{Equipment, EquipmentScore, StatType, EquipmentType};
use crate::config::ScoreConfig;

pub struct EquipmentScorer {
    config: ScoreConfig,
}

impl EquipmentScorer {
    pub fn new(config: ScoreConfig) -> Self {
        Self { config }
    }

    pub fn calculate_score(&self, equipment: &Equipment) -> EquipmentScore {
        let score = if equipment.is_recast {
            self.calculate_recast_score(equipment)
        } else {
            self.calculate_normal_score(equipment)
        };

        let recommendation = self.get_recommendation(equipment, score);
        let is_good_for_enhancement = self.is_good_for_enhancement(equipment, score);

        EquipmentScore {
            score,
            recommendation,
            is_good_for_enhancement,
        }
    }

    fn calculate_normal_score(&self, equipment: &Equipment) -> f32 {
        let mut score = 0.0;

        // 遍历副属性计算分数
        for stat in &equipment.sub_stats {
            score += match stat.stat_type {
                StatType::AttackPercent => stat.value,
                StatType::HealthPercent => stat.value,
                StatType::DefensePercent => stat.value,
                StatType::EffectHit => stat.value,
                StatType::EffectResistance => stat.value,
                StatType::Speed => stat.value * 2.0,
                StatType::CriticalRate => stat.value * (9.0 / 6.0),
                StatType::CriticalDamage => stat.value * (9.0 / 8.0),
                StatType::Attack => stat.value * 3.46 / 39.0,
                StatType::Defense => stat.value * 4.99 / 31.0,
                StatType::Health => stat.value * 3.09 / 174.0,
            };
        }

        score
    }

    fn calculate_recast_score(&self, equipment: &Equipment) -> f32 {
        // 重铸界面的分数计算逻辑
        // 这里可以计算重铸前后的分数差
        self.calculate_normal_score(equipment)
    }

    fn get_recommendation(&self, equipment: &Equipment, score: f32) -> String {
        if equipment.is_recast {
            return self.get_recast_recommendation(equipment, score);
        }

        let standards = self.get_standards_for_equipment(equipment);
        let level_group = self.get_level_group(equipment.enhancement_level);
        let threshold = standards[level_group];

        if score >= threshold {
            "建议强化".to_string()
        } else if self.is_speed_equipment(equipment) {
            self.check_speed_potential(equipment)
        } else {
            "不建议强化".to_string()
        }
    }

    fn get_recast_recommendation(&self, equipment: &Equipment, score: f32) -> String {
        let threshold = if equipment.equipment_type.is_left_three() {
            self.config.recast_standards[0] // 67
        } else {
            self.config.recast_standards[1] // 65
        };

        if score >= threshold {
            "建议重铸".to_string()
        } else {
            "不建议重铸".to_string()
        }
    }

    fn is_good_for_enhancement(&self, equipment: &Equipment, score: f32) -> bool {
        if equipment.is_recast {
            let threshold = if equipment.equipment_type.is_left_three() {
                self.config.recast_standards[0]
            } else {
                self.config.recast_standards[1]
            };
            return score >= threshold;
        }

        let standards = self.get_standards_for_equipment(equipment);
        let level_group = self.get_level_group(equipment.enhancement_level);
        let threshold = standards[level_group];

        score >= threshold || (self.is_speed_equipment(equipment) && self.has_speed_potential(equipment))
    }

    fn get_standards_for_equipment(&self, equipment: &Equipment) -> &[f32] {
        match equipment.equipment_type {
            EquipmentType::Weapon | EquipmentType::Helmet | EquipmentType::Armor => {
                &self.config.left_three_standards
            },
            EquipmentType::Necklace | EquipmentType::Ring => {
                &self.config.right_three_standards
            },
            EquipmentType::Boots => {
                // 鞋子：主属性是速度时使用右三标准，否则使用左三标准
                if equipment.main_stat.stat_type == StatType::Speed {
                    &self.config.right_three_standards
                } else {
                    &self.config.left_three_standards
                }
            },
        }
    }

    fn get_level_group(&self, enhancement_level: u8) -> usize {
        match enhancement_level {
            0..=2 => 0,
            3..=5 => 1,
            6..=8 => 2,
            9..=11 => 3,
            12..=14 => 4,
            15.. => 5,
        }
    }

    fn is_speed_equipment(&self, equipment: &Equipment) -> bool {
        // 检查是否有速度副属性
        equipment.sub_stats.iter().any(|stat| stat.stat_type == StatType::Speed)
    }

    fn check_speed_potential(&self, equipment: &Equipment) -> String {
        if self.has_speed_potential(equipment) {
            "可作为赌速度装备".to_string()
        } else {
            "不建议强化".to_string()
        }
    }

    fn has_speed_potential(&self, equipment: &Equipment) -> bool {
        // 检查速度属性是否满足标准
        if let Some(speed_stat) = equipment.sub_stats.iter().find(|stat| stat.stat_type == StatType::Speed) {
            let level_group = self.get_level_group(equipment.enhancement_level);
            let speed_threshold = self.config.speed_standards[level_group];
            return speed_stat.value >= speed_threshold;
        }
        false
    }

    pub fn get_detailed_analysis(&self, equipment: &Equipment) -> String {
        let mut analysis = String::new();
        
        analysis.push_str(&format!("装备类型: {} {}\n", 
            equipment.rarity.to_str(), 
            equipment.equipment_type.to_str()));
        
        analysis.push_str(&format!("强化等级: +{}\n", equipment.enhancement_level));
        
        if let Some(set_type) = &equipment.set_type {
            analysis.push_str(&format!("套装: {}\n", set_type));
        }
        
        analysis.push_str(&format!("主属性: {} {}\n", 
            equipment.main_stat.stat_type.to_str(),
            if equipment.main_stat.is_percentage { 
                format!("{}%", equipment.main_stat.value) 
            } else { 
                equipment.main_stat.value.to_string() 
            }));
        
        analysis.push_str("副属性:\n");
        for stat in &equipment.sub_stats {
            analysis.push_str(&format!("  {}: {}\n", 
                stat.stat_type.to_str(),
                if stat.is_percentage { 
                    format!("{}%", stat.value) 
                } else { 
                    stat.value.to_string() 
                }));
        }
        
        let score_result = self.calculate_score(equipment);
        analysis.push_str(&format!("\n总分: {:.1}\n", score_result.score));
        analysis.push_str(&format!("建议: {}\n", score_result.recommendation));
        
        analysis
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::equipment::types::{Rarity, Stat};

    fn create_test_equipment() -> Equipment {
        Equipment {
            rarity: Rarity::Legendary,
            equipment_type: EquipmentType::Weapon,
            enhancement_level: 15,
            set_type: None,
            main_stat: Stat::new(StatType::Attack, 525.0),
            sub_stats: vec![
                Stat::new(StatType::AttackPercent, 8.0),
                Stat::new(StatType::CriticalRate, 5.0),
                Stat::new(StatType::CriticalDamage, 7.0),
                Stat::new(StatType::Speed, 4.0),
            ],
            is_recast: false,
        }
    }

    #[test]
    fn test_score_calculation() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        let equipment = create_test_equipment();
        
        let result = scorer.calculate_score(&equipment);
        
        // 验证分数计算
        // 8 + 5*(9/6) + 7*(9/8) + 4*2 = 8 + 7.5 + 7.875 + 8 = 31.375
        assert!((result.score - 31.375).abs() < 0.1);
    }

    #[test]
    fn test_recommendation() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        let equipment = create_test_equipment();
        
        let result = scorer.calculate_score(&equipment);
        
        // 对于+15的武器，标准是55，31.375 < 55，所以不建议强化
        assert_eq!(result.recommendation, "不建议强化");
        assert!(!result.is_good_for_enhancement);
    }

    #[test]
    fn test_speed_equipment() {
        let config = ScoreConfig::default();
        let scorer = EquipmentScorer::new(config);
        let equipment = create_test_equipment();
        
        assert!(scorer.is_speed_equipment(&equipment));
        
        // 速度4，对于+15装备标准是14，不满足速度标准
        assert!(!scorer.has_speed_potential(&equipment));
    }
}
