use e7gearocr_lib::config::{AppConfig, ConfigManager};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config_path = ConfigManager::get_config_path();

    if config_path.exists() {
        println!("配置文件已存在: {:?}", config_path);
        return Ok(());
    }

    let config = AppConfig::default();
    ConfigManager::save_config(&config)?;

    println!("默认配置文件已生成: {:?}", config_path);
    println!("配置内容:");
    println!("{}", serde_json::to_string_pretty(&config)?);

    Ok(())
}
