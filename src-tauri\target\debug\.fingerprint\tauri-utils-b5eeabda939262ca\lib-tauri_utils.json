{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 12567278105657371571, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9881324285225764361], [3150220818285335163, "url", false, 12542923243119232473], [3191507132440681679, "serde_untagged", false, 5920297652150408445], [4071963112282141418, "serde_with", false, 9704829955122446450], [4899080583175475170, "semver", false, 7808462889705218313], [5986029879202738730, "log", false, 2334386541417879784], [6606131838865521726, "ctor", false, 15681567242682283708], [7170110829644101142, "json_patch", false, 10310247958795287513], [8319709847752024821, "uuid", false, 10025126449656590354], [8569119365930580996, "serde_json", false, 13470422440041892068], [9010263965687315507, "http", false, 16749436415575297115], [9451456094439810778, "regex", false, 15358229445842995953], [9556762810601084293, "brotli", false, 3056490939896555522], [9689903380558560274, "serde", false, 13238231853154323401], [10806645703491011684, "thiserror", false, 7309883229814674106], [11989259058781683633, "dunce", false, 13687696130865513278], [13625485746686963219, "anyhow", false, 11743789584046627553], [15609422047640926750, "toml", false, 14777024861631801177], [15622660310229662834, "walkdir", false, 6790808365779106652], [15932120279885307830, "memchr", false, 6619720700192008090], [17146114186171651583, "infer", false, 8778286877456956252], [17155886227862585100, "glob", false, 14140733676001939860], [17186037756130803222, "phf", false, 14119931159290271598]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-b5eeabda939262ca\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}