#[cfg(test)]
mod tests {
    use crate::config::{App<PERSON>onfig, ADBConfig, ConfigManager};
    use std::fs;

    #[test]
    fn test_default_config() {
        let config = AppConfig::default();
        
        assert_eq!(config.adb_settings.port, 16384);
        assert_eq!(config.adb_settings.timeout_seconds, 30);
        assert!(!config.adb_settings.auto_connect);
        
        assert!(!config.hotkey_settings.enabled);
        assert_eq!(config.hotkey_settings.key, "F1");
        
        assert_eq!(config.ocr_settings.page_detection_threshold, 0.8);
        assert_eq!(config.ocr_settings.equipment_detection_threshold, 0.7);
        assert_eq!(config.ocr_settings.border_threshold, 0.6);
        
        assert_eq!(config.score_settings.left_three_standards.len(), 6);
        assert_eq!(config.score_settings.right_three_standards.len(), 6);
        assert_eq!(config.score_settings.speed_standards.len(), 6);
        assert_eq!(config.score_settings.recast_standards.len(), 2);
    }

    #[test]
    fn test_config_serialization() {
        let config = AppConfig::default();
        
        // 测试序列化
        let json = serde_json::to_string(&config).unwrap();
        assert!(!json.is_empty());
        
        // 测试反序列化
        let deserialized: AppConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.adb_settings.port, config.adb_settings.port);
    }

    #[test]
    fn test_config_save_load() {
        let test_config = AppConfig {
            adb_settings: ADBConfig {
                port: 12345,
                timeout_seconds: 60,
                auto_connect: true,
            },
            ..Default::default()
        };

        // 保存配置
        ConfigManager::save_config(&test_config).unwrap();
        
        // 加载配置
        let loaded_config = ConfigManager::load_config().unwrap();
        
        assert_eq!(loaded_config.adb_settings.port, 12345);
        assert_eq!(loaded_config.adb_settings.timeout_seconds, 60);
        assert!(loaded_config.adb_settings.auto_connect);
        
        // 清理测试文件
        let config_path = ConfigManager::get_config_path();
        if config_path.exists() {
            fs::remove_file(config_path).ok();
        }
    }

    #[test]
    fn test_config_load_nonexistent() {
        // 确保配置文件不存在
        let config_path = ConfigManager::get_config_path();
        if config_path.exists() {
            fs::remove_file(&config_path).ok();
        }
        
        // 加载不存在的配置文件应该创建默认配置
        let config = ConfigManager::load_config().unwrap();
        
        // 验证是默认配置
        let default_config = AppConfig::default();
        assert_eq!(config.adb_settings.port, default_config.adb_settings.port);
        
        // 验证文件已创建
        assert!(config_path.exists());
        
        // 清理测试文件
        fs::remove_file(config_path).ok();
    }
}
