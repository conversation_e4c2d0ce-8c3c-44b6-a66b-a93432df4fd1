{"rustc": 1842507548689473721, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 15657897354478470176, "path": 10121698694593235643, "deps": [[2687729594444538932, "debug_unreachable", false, 16288798173320868384], [2828590642173593838, "cfg_if", false, 6055996481562514448], [3722963349756955755, "once_cell", false, 8172514313461330740], [4684437522915235464, "libc", false, 7687513178810142175], [5157631553186200874, "num_traits", false, 9033334885267581759], [5237962722597217121, "simd_helpers", false, 17071840944133545949], [5626665093607998638, "build_script_build", false, 4081068118271814005], [5986029879202738730, "log", false, 2334386541417879784], [6697151524989202978, "profiling", false, 9687780211755491606], [7074416887430417773, "av1_grain", false, 12535842964732364073], [8008191657135824715, "thiserror", false, 14062577474178705609], [11263754829263059703, "num_derive", false, 17939016997189001649], [12672448913558545127, "noop_proc_macro", false, 2627109131201781858], [13847662864258534762, "arrayvec", false, 5652799916889757540], [14931062873021150766, "itertools", false, 13536610234027556952], [15325537792103828505, "v_frame", false, 17638177118273114704], [16507960196461048755, "rayon", false, 9774995828945484045], [17605717126308396068, "paste", false, 4441269238326075440], [17706129463675219700, "arg_enum_proc_macro", false, 16961064753451042168], [17933778289016427379, "bitstream_io", false, 7469493218028014642]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-42997a7656c8dcbc\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}